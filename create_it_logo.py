#!/usr/bin/env python3
"""
<PERSON>ript to replace the white diagonal line in vexita_logo.png with "IT" text using Kenia font
"""

from PIL import Image, ImageDraw, ImageFont
import numpy as np
import os

def download_kenia_font():
    """Download Kenia font if not present"""
    font_path = "Kenia-Regular.ttf"
    if not os.path.exists(font_path):
        print("Downloading Kenia font...")
        import urllib.request
        url = "https://github.com/google/fonts/raw/main/ofl/kenia/Kenia-Regular.ttf"
        urllib.request.urlretrieve(url, font_path)
        print("Font downloaded successfully!")
    return font_path

def trim_whitespace(image):
    """Remove whitespace from around the image"""
    if image.mode != 'RGBA':
        image = image.convert('RGBA')
    
    bbox = image.getbbox()
    if bbox:
        return image.crop(bbox)
    else:
        return image

def find_white_diagonal_line(image):
    """Find and remove the white diagonal line in the V shape"""
    # Convert to numpy array for analysis
    img_array = np.array(image)
    height, width = img_array.shape[:2]
    
    # The V is roughly in the left portion of the image
    # Look for white/light colored pixels in the V area
    v_left = 0
    v_right = width // 3
    v_top = height // 4
    v_bottom = height * 3 // 4
    
    # Find white/light pixels (the diagonal line)
    white_pixels = []
    for y in range(v_top, v_bottom):
        for x in range(v_left, v_right):
            if x < width and y < height:
                pixel = img_array[y, x]
                if len(pixel) >= 3:
                    # Check if pixel is white or very light
                    r, g, b = pixel[:3]
                    if r > 200 and g > 200 and b > 200:  # Light/white pixel
                        white_pixels.append((x, y))
    
    return white_pixels

def remove_white_line_and_add_it(image, font_path):
    """Remove the white diagonal line and replace with IT text"""
    result_image = image.copy()
    draw = ImageDraw.Draw(result_image)
    
    width, height = image.size
    
    # Find the white diagonal line
    white_pixels = find_white_diagonal_line(image)
    
    # Sample the V's blue color from a clean area
    v_color = (35, 119, 166, 255)  # Default blue
    
    # Try to sample actual V color
    sample_x = width // 8
    sample_y = height // 3
    try:
        sampled_color = image.getpixel((sample_x, sample_y))
        if len(sampled_color) >= 3:
            # Check if it's blue-ish (V color)
            r, g, b = sampled_color[:3]
            if b > r and b > g and b > 100:
                v_color = sampled_color
    except:
        pass
    
    # Remove the white line by painting over it with V color
    if white_pixels:
        # Find bounding box of white pixels
        min_x = min(p[0] for p in white_pixels)
        max_x = max(p[0] for p in white_pixels)
        min_y = min(p[1] for p in white_pixels)
        max_y = max(p[1] for p in white_pixels)
        
        # Paint over the white line area
        margin = 5
        draw.rectangle([min_x - margin, min_y - margin, max_x + margin, max_y + margin], 
                       fill=v_color)
        
        print(f"Removed white line from area: ({min_x}, {min_y}) to ({max_x}, {max_y})")
        
        # Calculate center of the removed line for text placement
        text_center_x = (min_x + max_x) // 2
        text_center_y = (min_y + max_y) // 2
    else:
        # If no white line found, place text in center of V
        text_center_x = width // 6
        text_center_y = height // 2
        print("No white line detected, placing text in estimated V center")
    
    # Load Kenia font
    try:
        font_size = max(width // 20, 30)
        font = ImageFont.truetype(font_path, font_size)
        print(f"Using Kenia font with size {font_size}")
    except (OSError, IOError):
        print(f"Could not load {font_path}, using default font")
        font = ImageFont.load_default()
    
    text = "IT"
    
    # Get text dimensions
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    # Position text in center of where the white line was
    text_x = text_center_x - text_width // 2
    text_y = text_center_y - text_height // 2
    
    # Add the IT text with black color for contrast
    text_color = (0, 0, 0, 255)  # Black
    draw.text((text_x, text_y), text, font=font, fill=text_color)
    
    print(f"Added 'IT' text at position ({text_x}, {text_y})")
    print(f"Text size: {text_width} x {text_height}")
    print(f"Text center was: ({text_center_x}, {text_center_y})")
    
    return result_image

def main():
    # Download font if needed
    font_path = download_kenia_font()
    
    # Input and output file paths
    input_path = "public/vexita_logo.png"
    output_path = "public/vexita_it_logo_new.png"
    
    # Check if input file exists
    if not os.path.exists(input_path):
        print(f"Error: Input file {input_path} not found")
        return
    
    # Open the original image
    print(f"Opening {input_path}...")
    original_image = Image.open(input_path)
    print(f"Original image size: {original_image.size}")
    
    # Trim whitespace
    print("Trimming whitespace...")
    cropped_image = trim_whitespace(original_image)
    print(f"Cropped image size: {cropped_image.size}")
    
    # Remove white line and add IT text
    print("Removing white diagonal line and adding IT text...")
    final_image = remove_white_line_and_add_it(cropped_image, font_path)
    
    # Save the final image
    print(f"Saving final image to {output_path}...")
    final_image.save(output_path, "PNG")
    print("Done!")
    
    # Clean up font file
    if os.path.exists(font_path):
        os.remove(font_path)

if __name__ == "__main__":
    main()
