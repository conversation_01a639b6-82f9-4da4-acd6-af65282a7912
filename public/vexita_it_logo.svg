<svg width="1024" height="300" viewBox="0 0 1024 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background (transparent) -->
  <rect width="1024" height="300" fill="transparent"/>

  <!-- V Icon with IT text -->
  <g transform="translate(50, 50)">
    <!-- V Shape Background - proper inverted triangle with rounded top -->
    <path d="M30 0
             C10 0 0 10 0 30
             L0 40
             L100 180
             L200 40
             L200 30
             C200 10 190 0 170 0
             L30 0
             Z"
          fill="#3982a3"
          stroke="none"/>

    <!-- IT Text inside V -->
    <text x="100" y="80"
          font-family="Arial, sans-serif"
          font-size="32"
          font-weight="bold"
          fill="white"
          text-anchor="middle"
          dominant-baseline="middle">IT</text>
  </g>

  <!-- Vexita Text -->
  <g transform="translate(300, 50)">
    <text x="0" y="120"
          font-family="Arial, sans-serif"
          font-size="100"
          font-weight="normal"
          fill="#3982a3"
          dominant-baseline="middle">vexita</text>
  </g>
</svg>
